﻿<?xml version="1.0" encoding="UTF-8"?>
<project
    xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

<!--    <parent>-->
<!--        <groupId>org.springframework.boot</groupId>-->
<!--        <artifactId>spring-boot-starter-parent</artifactId>-->
<!--        <version>3.5.3</version>-->
<!--        <relativePath/>-->
<!--    </parent>-->
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.3.1</version> <relativePath/>
    </parent>

    <groupId>com.sofa</groupId>
    <artifactId>graphql</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>GraphQlAPI</name>
    <description>Demo project for Spring Boot</description>

    <properties>
        <kotlin.version>1.9.0</kotlin.version>
        <kotlinx.serialization.version>1.6.0</kotlinx.serialization.version>
        <ktor.version>2.3.5</ktor.version>
        <graphql-kotlin-client.version>7.0.0</graphql-kotlin-client.version>
        <lombok.version>1.18.30</lombok.version>
        <java.version>17</java.version>
    </properties>

    <dependencies>
        <!-- Kotlin Standard Library -->
       <dependency>
  <groupId>org.jetbrains.kotlin</groupId>
  <artifactId>kotlin-stdlib-jdk8</artifactId>
</dependency>
<dependency>
  <groupId>org.jetbrains.kotlin</groupId>
  <artifactId>kotlin-reflect</artifactId>
</dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>logging-interceptor</artifactId>
            <version>4.12.0</version> </dependency>
    <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
        <version>2.17.1</version>
    </dependency>
    <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-core</artifactId>
        <version>2.17.1</version>
    </dependency>
    <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-annotations</artifactId>
        <version>2.17.1</version>
    </dependency>

    <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-stdlib-jdk8</artifactId>
        <version>1.9.23</version> </dependency>

        <!-- Kotlinx Serialization -->
        <dependency>
            <groupId>org.jetbrains.kotlinx</groupId>
            <artifactId>kotlinx-serialization-json</artifactId>
            <version>${kotlinx.serialization.version}</version>
        </dependency>
        <dependency>
  <groupId>org.springframework.boot</groupId>
  <artifactId>spring-boot-starter-webflux</artifactId>
</dependency>

<dependency>
  <groupId>com.expediagroup</groupId>
  <artifactId>graphql-kotlin-ktor-client</artifactId>
  <version>${graphql-kotlin-client.version}</version>
</dependency>
<!-- تمكين ContentNegotiation + JSON serialization -->
<dependency>
  <groupId>io.ktor</groupId>
  <artifactId>ktor-serialization-kotlinx-json</artifactId>
  <version>${ktor.version}</version>
</dependency>

        <!-- GraphQL Kotlin Client -->
        <dependency>
            <groupId>com.expediagroup</groupId>
            <artifactId>graphql-kotlin-client</artifactId>
            <version>${graphql-kotlin-client.version}</version>
        </dependency>

        <!-- GraphQL Ktor Client -->
        <dependency>
            <groupId>com.expediagroup</groupId>
            <artifactId>graphql-kotlin-ktor-client</artifactId>
            <version>${graphql-kotlin-client.version}</version>
        </dependency>

        <!-- Ktor HTTP Clients -->
        <dependency>
            <groupId>io.ktor</groupId>
            <artifactId>ktor-client-core-jvm</artifactId>
            <version>${ktor.version}</version>
        </dependency>
        <dependency>
            <groupId>io.ktor</groupId>
            <artifactId>ktor-client-content-negotiation</artifactId>
            <version>${ktor.version}</version>
        </dependency>
        <dependency>
            <groupId>io.ktor</groupId>
            <artifactId>ktor-client-apache</artifactId>
            <version>${ktor.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
            <version>2.2.0</version> <!-- إصدار مجرب ومستقر -->
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.14.0</version> <!-- إصدار آمن -->
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>

        </dependency>        <dependency>
            <groupId>io.ktor</groupId>
            <artifactId>ktor-client-cio</artifactId>
            <version>${ktor.version}</version>
        </dependency>

        <!-- Spring Boot Dependencies -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>runtime</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-kotlin</artifactId>
            <version>2.17.1</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.graphql</groupId>
            <artifactId>spring-graphql</artifactId>

        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Database & Tools -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-core</artifactId>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- GraphQL Kotlin Maven Plugin -->
            <plugin>
                <groupId>com.expediagroup</groupId>
                <artifactId>graphql-kotlin-maven-plugin</artifactId>
                <version>${graphql-kotlin-client.version}</version>
                <configuration>
        <endpoint>http://localhost:8080/v1/graphql</endpoint>
        <schemaFile>${project.basedir}/src/main/resources/graphql/schema.graphql</schemaFile>

        <packageName>com.sofa.generated</packageName>
    </configuration>
                <executions>
                    <execution>
                        <id>generate-client</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>generate-client</goal>
                        </goals>
                        <configuration>

                            <schemaFile>src/main/resources/graphql/schema.graphql</schemaFile>

                            <queryFiles>
                                <queryFile>src/main/resources/graphql/GetAllStudents.graphql</queryFile>
                                <queryFile>src/main/resources/graphql/InsertNewStudent.graphql</queryFile>
                                <queryFile>src/main/resources/graphql/GetStudentById.graphql</queryFile>
                                <queryFile>src/main/resources/graphql/DeleteStudentById.graphql</queryFile>
                                <queryFile>src/main/resources/graphql/UpdateStudentById.graphql</queryFile>
                            </queryFiles>
                            <packageName>com.sofa.generated</packageName>
                        </configuration>
                    </execution>


                </executions>
            </plugin>

            <!-- Kotlin Maven Plugin -->
            <plugin>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-maven-plugin</artifactId>
                <version>${kotlin.version}</version>
                <executions>
                    <execution>
                        <id>compile</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>test-compile</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>test-compile</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <jvmTarget>17</jvmTarget>
                </configuration>
            </plugin>

            <!-- Maven Compiler Plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>

            <!-- Spring Boot Maven Plugin -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>



